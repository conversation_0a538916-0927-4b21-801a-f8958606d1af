import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';
import 'steps/welcome_step.dart';
import 'steps/basic_info_step.dart';
import 'steps/fitness_goals_step.dart';
import 'steps/fitness_level_step.dart';
import 'steps/preferences_step.dart';

class OnboardingFlow extends StatefulWidget {
  const OnboardingFlow({super.key});

  @override
  State<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends State<OnboardingFlow> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 5;
  
  final Map<String, dynamic> _onboardingData = {};

  final List<Widget> _steps = [
    const WelcomeStep(),
    const BasicInfoStep(),
    const FitnessGoalsStep(),
    const FitnessLevelStep(),
    const PreferencesStep(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFFFFFF),
        elevation: 0,
        leading: _currentStep > 0
            ? IconButton(
                icon: const Icon(
                  Icons.arrow_back,
                  color: Color(0xFF18181B),
                ),
                onPressed: _previousStep,
              )
            : null,
        title: _buildProgressIndicator(),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              itemCount: _steps.length,
              itemBuilder: (context, index) {
                return _steps[index];
              },
            ),
          ),
          _buildBottomNavigation(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      width: 200,
      height: 4,
      decoration: BoxDecoration(
        color: const Color(0xFFF4F4F5),
        borderRadius: BorderRadius.circular(2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: (_currentStep + 1) / _totalSteps,
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFF18181B),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          top: BorderSide(
            color: Color(0xFFF4F4F5),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            if (_currentStep > 0)
              Expanded(
                child: CustomButton(
                  text: 'Back',
                  onPressed: _previousStep,
                  isSecondary: true,
                ),
              ),
            if (_currentStep > 0) const SizedBox(width: 16),
            Expanded(
              flex: _currentStep > 0 ? 1 : 2,
              child: CustomButton(
                text: _currentStep == _totalSteps - 1 ? 'Complete' : 'Continue',
                onPressed: _nextStep,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  Future<void> _completeOnboarding() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    // Update user profile with onboarding data
    if (authProvider.userProfile != null) {
      final updatedProfile = authProvider.userProfile!.copyWith(
        displayName: _onboardingData['displayName'],
        age: _onboardingData['age'],
        gender: _onboardingData['gender'],
        height: _onboardingData['height'],
        weight: _onboardingData['weight'],
        fitnessGoalPrimary: _onboardingData['fitnessGoalPrimary'],
        fitnessGoalsArray: _onboardingData['fitnessGoalsArray'],
        cardioFitnessLevel: _onboardingData['cardioFitnessLevel'],
        weightliftingFitnessLevel: _onboardingData['weightliftingFitnessLevel'],
        equipment: _onboardingData['equipment'],
        workoutDays: _onboardingData['workoutDays'],
        workoutDurationPreference: _onboardingData['workoutDurationPreference'],
        onboardingCompleted: true,
        updatedAt: DateTime.now(),
      );
      
      final success = await authProvider.updateUserProfile(updatedProfile);
      
      if (success && mounted) {
        Navigator.pushReplacementNamed(context, '/home');
      }
    }
  }

  void updateOnboardingData(Map<String, dynamic> data) {
    setState(() {
      _onboardingData.addAll(data);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}
