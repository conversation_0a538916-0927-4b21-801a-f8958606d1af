import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class WelcomeStep extends StatelessWidget {
  const WelcomeStep({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon or Illustration
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFF18181B),
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.fitness_center,
              size: 60,
              color: Color(0xFFFFFFFF),
            ),
          ),
          
          const SizedBox(height: 40),
          
          // Welcome Title
          Text(
            'Welcome to OpenFit!',
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF18181B),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Welcome Description
          Text(
            'Let\'s personalize your fitness journey. We\'ll ask you a few questions to create the perfect workout plan just for you.',
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 16,
              color: const Color(0xFF71717A),
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 40),
          
          // Features List
          Column(
            children: [
              _buildFeatureItem(
                icon: Icons.person_outline,
                title: 'Personalized Plans',
                description: 'Workouts tailored to your goals and fitness level',
              ),
              const SizedBox(height: 20),
              _buildFeatureItem(
                icon: Icons.track_changes_outlined,
                title: 'Progress Tracking',
                description: 'Monitor your improvements and celebrate milestones',
              ),
              const SizedBox(height: 20),
              _buildFeatureItem(
                icon: Icons.schedule_outlined,
                title: 'Flexible Scheduling',
                description: 'Fit workouts into your busy lifestyle',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFFF4F4F5),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Icon(
            icon,
            size: 24,
            color: const Color(0xFF18181B),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF18181B),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF71717A),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
