import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PreferencesStep extends StatefulWidget {
  const PreferencesStep({super.key});

  @override
  State<PreferencesStep> createState() => _PreferencesStepState();
}

class _PreferencesStepState extends State<PreferencesStep> {
  final List<String> _selectedEquipment = [];
  final List<String> _selectedWorkoutDays = [];
  String? _workoutDuration;

  final List<Map<String, dynamic>> _equipment = [
    {'id': 'bodyweight', 'title': 'Bodyweight Only', 'icon': Icons.accessibility},
    {'id': 'dumbbells', 'title': 'Dumbbells', 'icon': Icons.fitness_center},
    {'id': 'barbell', 'title': 'Barbell', 'icon': Icons.fitness_center},
    {'id': 'resistance_bands', 'title': 'Resistance Bands', 'icon': Icons.linear_scale},
    {'id': 'kettlebells', 'title': 'Kettlebells', 'icon': Icons.sports_gymnastics},
    {'id': 'pull_up_bar', 'title': 'Pull-up Bar', 'icon': Icons.horizontal_rule},
    {'id': 'gym_access', 'title': 'Full Gym Access', 'icon': Icons.business},
  ];

  final List<String> _workoutDays = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];

  final List<Map<String, String>> _durations = [
    {'id': '15-30', 'title': '15-30 minutes'},
    {'id': '30-45', 'title': '30-45 minutes'},
    {'id': '45-60', 'title': '45-60 minutes'},
    {'id': '60+', 'title': '60+ minutes'},
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            
            // Header
            Text(
              'Workout Preferences',
              style: GoogleFonts.inter(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Tell us about your workout preferences so we can create the perfect plan for you.',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF71717A),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Equipment Section
            Text(
              'Available Equipment',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select all equipment you have access to:',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF71717A),
              ),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _equipment.map((equipment) {
                final isSelected = _selectedEquipment.contains(equipment['id']);
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedEquipment.remove(equipment['id']);
                      } else {
                        _selectedEquipment.add(equipment['id']);
                      }
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? const Color(0xFF18181B)
                          : const Color(0xFFFFFFFF),
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFF18181B)
                            : const Color(0xFFE4E4E7),
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          equipment['icon'],
                          size: 20,
                          color: isSelected 
                              ? const Color(0xFFFFFFFF)
                              : const Color(0xFF18181B),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          equipment['title'],
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isSelected 
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF18181B),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 32),
            
            // Workout Days Section
            Text(
              'Preferred Workout Days',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select the days you prefer to work out:',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF71717A),
              ),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _workoutDays.map((day) {
                final isSelected = _selectedWorkoutDays.contains(day);
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedWorkoutDays.remove(day);
                      } else {
                        _selectedWorkoutDays.add(day);
                      }
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? const Color(0xFF18181B)
                          : const Color(0xFFFFFFFF),
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFF18181B)
                            : const Color(0xFFE4E4E7),
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      day.substring(0, 3), // Show first 3 letters
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected 
                            ? const Color(0xFFFFFFFF)
                            : const Color(0xFF18181B),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 32),
            
            // Workout Duration Section
            Text(
              'Preferred Workout Duration',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'How long do you prefer your workouts to be?',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF71717A),
              ),
            ),
            const SizedBox(height: 16),
            
            Column(
              children: _durations.map((duration) {
                final isSelected = _workoutDuration == duration['id'];
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _workoutDuration = duration['id'];
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? const Color(0xFF18181B)
                          : const Color(0xFFFFFFFF),
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFF18181B)
                            : const Color(0xFFE4E4E7),
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? const Color(0xFFFFFFFF)
                                : Colors.transparent,
                            border: Border.all(
                              color: isSelected 
                                  ? const Color(0xFFFFFFFF)
                                  : const Color(0xFFE4E4E7),
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  size: 14,
                                  color: Color(0xFF18181B),
                                )
                              : null,
                        ),
                        const SizedBox(width: 16),
                        Text(
                          duration['title']!,
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isSelected 
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF18181B),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
