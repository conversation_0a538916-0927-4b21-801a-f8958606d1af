import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FitnessLevelStep extends StatefulWidget {
  const FitnessLevelStep({super.key});

  @override
  State<FitnessLevelStep> createState() => _FitnessLevelStepState();
}

class _FitnessLevelStepState extends State<FitnessLevelStep> {
  int? _cardioLevel;
  int? _strengthLevel;

  final List<Map<String, dynamic>> _fitnessLevels = [
    {
      'level': 1,
      'title': 'Beginner',
      'cardioDescription': 'I rarely do cardio or get out of breath easily',
      'strengthDescription': 'I rarely lift weights or do strength training',
    },
    {
      'level': 2,
      'title': 'Novice',
      'cardioDescription': 'I do light cardio occasionally (1-2 times per week)',
      'strengthDescription': 'I do some strength training occasionally',
    },
    {
      'level': 3,
      'title': 'Intermediate',
      'cardioDescription': 'I do regular cardio (3-4 times per week)',
      'strengthDescription': 'I strength train regularly with good form',
    },
    {
      'level': 4,
      'title': 'Advanced',
      'cardioDescription': 'I do intense cardio regularly and have good endurance',
      'strengthDescription': 'I lift heavy weights with excellent form',
    },
    {
      'level': 5,
      'title': 'Expert',
      'cardioDescription': 'I\'m an athlete or have exceptional cardiovascular fitness',
      'strengthDescription': 'I\'m very experienced with advanced techniques',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            
            // Header
            Text(
              'What\'s your fitness level?',
              style: GoogleFonts.inter(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Help us understand your current fitness level so we can create the right workout intensity for you.',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF71717A),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Cardio Fitness Section
            Text(
              'Cardio Fitness Level',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 16),
            
            Column(
              children: _fitnessLevels.map((level) {
                final isSelected = _cardioLevel == level['level'];
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _cardioLevel = level['level'];
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? const Color(0xFF18181B)
                          : const Color(0xFFFFFFFF),
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFF18181B)
                            : const Color(0xFFE4E4E7),
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFFF4F4F5),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Center(
                            child: Text(
                              level['level'].toString(),
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: isSelected 
                                    ? const Color(0xFF18181B)
                                    : const Color(0xFF71717A),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                level['title'],
                                style: GoogleFonts.inter(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected 
                                      ? const Color(0xFFFFFFFF)
                                      : const Color(0xFF18181B),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                level['cardioDescription'],
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: isSelected 
                                      ? const Color(0xFFE4E4E7)
                                      : const Color(0xFF71717A),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 32),
            
            // Strength Training Section
            Text(
              'Strength Training Level',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 16),
            
            Column(
              children: _fitnessLevels.map((level) {
                final isSelected = _strengthLevel == level['level'];
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _strengthLevel = level['level'];
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? const Color(0xFF18181B)
                          : const Color(0xFFFFFFFF),
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFF18181B)
                            : const Color(0xFFE4E4E7),
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFFF4F4F5),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Center(
                            child: Text(
                              level['level'].toString(),
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: isSelected 
                                    ? const Color(0xFF18181B)
                                    : const Color(0xFF71717A),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                level['title'],
                                style: GoogleFonts.inter(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected 
                                      ? const Color(0xFFFFFFFF)
                                      : const Color(0xFF18181B),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                level['strengthDescription'],
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: isSelected 
                                      ? const Color(0xFFE4E4E7)
                                      : const Color(0xFF71717A),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
