import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FitnessGoalsStep extends StatefulWidget {
  const FitnessGoalsStep({super.key});

  @override
  State<FitnessGoalsStep> createState() => _FitnessGoalsStepState();
}

class _FitnessGoalsStepState extends State<FitnessGoalsStep> {
  String? _primaryGoal;
  final List<String> _selectedGoals = [];

  final List<Map<String, dynamic>> _fitnessGoals = [
    {
      'id': 'lose_weight',
      'title': 'Lose Weight',
      'description': 'Burn calories and reduce body fat',
      'icon': Icons.trending_down,
    },
    {
      'id': 'build_muscle',
      'title': 'Build Muscle',
      'description': 'Increase muscle mass and strength',
      'icon': Icons.fitness_center,
    },
    {
      'id': 'improve_endurance',
      'title': 'Improve Endurance',
      'description': 'Boost cardiovascular fitness',
      'icon': Icons.directions_run,
    },
    {
      'id': 'increase_flexibility',
      'title': 'Increase Flexibility',
      'description': 'Improve mobility and range of motion',
      'icon': Icons.self_improvement,
    },
    {
      'id': 'general_fitness',
      'title': 'General Fitness',
      'description': 'Overall health and wellness',
      'icon': Icons.favorite,
    },
    {
      'id': 'sport_specific',
      'title': 'Sport Specific',
      'description': 'Train for a specific sport or activity',
      'icon': Icons.sports_soccer,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            
            // Header
            Text(
              'What are your fitness goals?',
              style: GoogleFonts.inter(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select your primary goal and any additional goals you\'d like to work towards.',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: const Color(0xFF71717A),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Primary Goal Section
            Text(
              'Primary Goal',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 12),
            
            // Goals Grid
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
              ),
              itemCount: _fitnessGoals.length,
              itemBuilder: (context, index) {
                final goal = _fitnessGoals[index];
                final isPrimary = _primaryGoal == goal['id'];
                final isSelected = _selectedGoals.contains(goal['id']);
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (_primaryGoal == goal['id']) {
                        // If tapping the current primary goal, deselect it
                        _primaryGoal = null;
                        _selectedGoals.remove(goal['id']);
                      } else {
                        // Set as primary goal
                        _primaryGoal = goal['id'];
                        if (!_selectedGoals.contains(goal['id'])) {
                          _selectedGoals.add(goal['id']);
                        }
                      }
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isPrimary 
                          ? const Color(0xFF18181B) 
                          : isSelected 
                              ? const Color(0xFFF4F4F5)
                              : const Color(0xFFFFFFFF),
                      border: Border.all(
                        color: isPrimary 
                            ? const Color(0xFF18181B)
                            : isSelected
                                ? const Color(0xFF18181B)
                                : const Color(0xFFE4E4E7),
                        width: isPrimary || isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          goal['icon'],
                          size: 32,
                          color: isPrimary 
                              ? const Color(0xFFFFFFFF)
                              : const Color(0xFF18181B),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          goal['title'],
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isPrimary 
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF18181B),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          goal['description'],
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            color: isPrimary 
                                ? const Color(0xFFE4E4E7)
                                : const Color(0xFF71717A),
                          ),
                        ),
                        if (isPrimary) ...[
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFFFFF),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'PRIMARY',
                              style: GoogleFonts.inter(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF18181B),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // Additional Goals Section
            Text(
              'Additional Goals (Optional)',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap any additional goals you\'d like to work on.',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF71717A),
              ),
            ),
            const SizedBox(height: 12),
            
            // Additional Goals List
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _fitnessGoals.map((goal) {
                final isSelected = _selectedGoals.contains(goal['id']);
                final isPrimary = _primaryGoal == goal['id'];
                
                if (isPrimary) return const SizedBox.shrink();
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedGoals.remove(goal['id']);
                      } else {
                        _selectedGoals.add(goal['id']);
                      }
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? const Color(0xFF18181B)
                          : const Color(0xFFFFFFFF),
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFF18181B)
                            : const Color(0xFFE4E4E7),
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      goal['title'],
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected 
                            ? const Color(0xFFFFFFFF)
                            : const Color(0xFF18181B),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
