import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/workout.dart';
import '../../providers/workout_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/loading_overlay.dart';

class WorkoutDetailScreen extends StatefulWidget {
  final Workout workout;

  const WorkoutDetailScreen({
    super.key,
    required this.workout,
  });

  @override
  State<WorkoutDetailScreen> createState() => _WorkoutDetailScreenState();
}

class _WorkoutDetailScreenState extends State<WorkoutDetailScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WorkoutProvider>(context, listen: false)
          .loadWorkout(widget.workout.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFFFFFF),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFF18181B),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.workout.name,
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF18181B),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.more_vert,
              color: Color(0xFF18181B),
            ),
            onPressed: () => _showWorkoutMenu(),
          ),
        ],
      ),
      body: Consumer<WorkoutProvider>(
        builder: (context, workoutProvider, child) {
          final workout = workoutProvider.currentWorkout ?? widget.workout;
          
          return LoadingOverlay(
            isLoading: workoutProvider.isLoading,
            child: Column(
              children: [
                // Workout Header
                _buildWorkoutHeader(workout, workoutProvider),
                
                // Exercise List
                Expanded(
                  child: _buildExerciseList(workout, workoutProvider),
                ),
                
                // Bottom Actions
                _buildBottomActions(workout, workoutProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWorkoutHeader(Workout workout, WorkoutProvider workoutProvider) {
    final isActive = workoutProvider.currentWorkout?.id == workout.id;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFF4F4F5),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      workout.name,
                      style: GoogleFonts.inter(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF18181B),
                      ),
                    ),
                    if (workout.notes != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        workout.notes!,
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: const Color(0xFF71717A),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (isActive) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF16A34A),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'ACTIVE',
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFFFFFFFF),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          
          if (isActive) ...[
            const SizedBox(height: 16),
            Text(
              workoutProvider.formattedElapsedTime,
              style: GoogleFonts.inter(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF16A34A),
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // Workout Stats
          Row(
            children: [
              _buildStatItem(
                'Exercises',
                '${workout.exercises?.length ?? 0}',
              ),
              const SizedBox(width: 24),
              _buildStatItem(
                'Completed',
                '${workout.exercises?.where((e) => e.completed).length ?? 0}',
              ),
              if (workout.duration != null) ...[
                const SizedBox(width: 24),
                _buildStatItem(
                  'Duration',
                  _formatDuration(workout.duration!),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF18181B),
          ),
        ),
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: 12,
            color: const Color(0xFF71717A),
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseList(Workout workout, WorkoutProvider workoutProvider) {
    final exercises = workout.exercises ?? [];
    
    if (exercises.isEmpty) {
      return _buildEmptyExerciseList();
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: exercises.length,
      itemBuilder: (context, index) {
        final exercise = exercises[index];
        return _buildExerciseCard(exercise, workoutProvider);
      },
    );
  }

  Widget _buildEmptyExerciseList() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.add_circle_outline,
            size: 64,
            color: const Color(0xFF71717A).withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No exercises yet',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF18181B),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add exercises to start your workout',
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF71717A),
            ),
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'Add Exercise',
            onPressed: () {
              // TODO: Navigate to exercise picker
            },
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseCard(WorkoutExercise exercise, WorkoutProvider workoutProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: exercise.completed 
            ? const Color(0xFFF0FDF4)
            : const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: exercise.completed 
              ? const Color(0xFF16A34A)
              : const Color(0xFFE5E7EB),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  exercise.name,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF18181B),
                    decoration: exercise.completed 
                        ? TextDecoration.lineThrough
                        : null,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  workoutProvider.updateWorkoutExercise(
                    workoutExerciseId: exercise.id,
                    completed: !exercise.completed,
                  );
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: exercise.completed 
                        ? const Color(0xFF16A34A)
                        : Colors.transparent,
                    border: Border.all(
                      color: exercise.completed 
                          ? const Color(0xFF16A34A)
                          : const Color(0xFFE5E7EB),
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: exercise.completed
                      ? const Icon(
                          Icons.check,
                          size: 16,
                          color: Color(0xFFFFFFFF),
                        )
                      : null,
                ),
              ),
            ],
          ),
          
          if (exercise.sets != null || exercise.reps != null || exercise.weight != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                if (exercise.sets != null)
                  _buildExerciseDetail('Sets', '${exercise.sets}'),
                if (exercise.reps != null) ...[
                  const SizedBox(width: 16),
                  _buildExerciseDetail('Reps', exercise.reps!.join(', ')),
                ],
                if (exercise.weight != null) ...[
                  const SizedBox(width: 16),
                  _buildExerciseDetail('Weight', '${exercise.weight!.join(', ')} kg'),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildExerciseDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: 12,
            color: const Color(0xFF71717A),
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF18181B),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomActions(Workout workout, WorkoutProvider workoutProvider) {
    final isActive = workoutProvider.currentWorkout?.id == workout.id;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          top: BorderSide(
            color: Color(0xFFF4F4F5),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Add Exercise',
                onPressed: () {
                  // TODO: Navigate to exercise picker
                },
                isSecondary: true,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomButton(
                text: isActive ? 'Finish Workout' : 'Start Workout',
                onPressed: () {
                  if (isActive) {
                    _showFinishWorkoutDialog(workoutProvider);
                  } else {
                    workoutProvider.startWorkout(workout.id);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showWorkoutMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Workout'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to edit workout
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Duplicate Workout'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Duplicate workout
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Workout', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteWorkoutDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFinishWorkoutDialog(WorkoutProvider workoutProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Finish Workout',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to finish this workout?',
          style: GoogleFonts.inter(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.inter(
                color: const Color(0xFF71717A),
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await workoutProvider.completeWorkout();
              if (success) {
                if (mounted) Navigator.pop(context);
              }
            },
            child: Text(
              'Finish',
              style: GoogleFonts.inter(
                color: const Color(0xFF16A34A),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteWorkoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Workout',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this workout? This action cannot be undone.',
          style: GoogleFonts.inter(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.inter(
                color: const Color(0xFF71717A),
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final workoutProvider = Provider.of<WorkoutProvider>(context, listen: false);
              final success = await workoutProvider.deleteWorkout(widget.workout.id);
              if (success) {
                if (mounted) Navigator.pop(context);
              }
            },
            child: Text(
              'Delete',
              style: GoogleFonts.inter(
                color: const Color(0xFFDC2626),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes}m ${remainingSeconds}s';
  }
}
