import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/workout_provider.dart';
import '../../widgets/custom_button.dart';

class WorkoutScreen extends StatefulWidget {
  const WorkoutScreen({super.key});

  @override
  State<WorkoutScreen> createState() => _WorkoutScreenState();
}

class _WorkoutScreenState extends State<WorkoutScreen> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WorkoutProvider>(context, listen: false).loadWorkouts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF2D2D2D),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),

            // Main Content
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // OpenFit Header
                    _buildOpenFitHeader(),

                    const SizedBox(height: 32),

                    // Ready to train section
                    _buildReadyToTrainSection(),

                    const SizedBox(height: 24),

                    // Workout Card
                    _buildWorkoutCard(),

                    const SizedBox(height: 32),

                    // This Week Section
                    _buildThisWeekSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Text(
            'workout overview',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: const Color(0xFFFFFFFF),
              fontWeight: FontWeight.w400,
            ),
          ),
          const Spacer(),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: const Color(0xFF16A34A),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOpenFitHeader() {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFF3B82F6),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.fitness_center,
            color: Color(0xFFFFFFFF),
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'OpenFit',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            Text(
              'by OpenThrive',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF71717A),
              ),
            ),
          ],
        ),
        const Spacer(),
        const Icon(
          Icons.help_outline,
          color: Color(0xFF71717A),
          size: 20,
        ),
      ],
    );
  }

  Widget _buildReadyToTrainSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ready to train?',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF18181B),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Your next workout is optimized and ready',
          style: GoogleFonts.inter(
            fontSize: 16,
            color: const Color(0xFF71717A),
          ),
        ),
      ],
    );
  }

  Widget _buildWorkoutCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workout Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.fitness_center,
                  color: Color(0xFFFFFFFF),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Back Workout',
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF18181B),
                    ),
                  ),
                  Text(
                    'Upper body strength',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF71717A),
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Workout Stats
          Row(
            children: [
              _buildStatItem(
                Icons.access_time,
                'Duration',
                '58 min',
                const Color(0xFF3B82F6),
              ),
              const SizedBox(width: 24),
              _buildStatItem(
                Icons.local_fire_department,
                'Calories',
                '254 kcal',
                const Color(0xFFEF4444),
              ),
              const SizedBox(width: 24),
              _buildStatItem(
                Icons.repeat,
                'Sets',
                '3 x 4',
                const Color(0xFF8B5CF6),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Today's Focus
          Text(
            'Today\'s Focus',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF18181B),
            ),
          ),

          const SizedBox(height: 12),

          // Focus Tags
          Row(
            children: [
              _buildFocusTag('Pull-ups'),
              const SizedBox(width: 8),
              _buildFocusTag('Rows'),
              const SizedBox(width: 8),
              _buildFocusTag('Lat pulldowns'),
            ],
          ),

          const SizedBox(height: 24),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Start Workout',
                  onPressed: () {
                    // TODO: Start workout
                  },
                  icon: Icons.play_arrow,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'View Details',
                  onPressed: () {
                    // TODO: View workout details
                  },
                  isSecondary: true,
                  icon: Icons.list,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String label, String value, Color color) {
    return Column(
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: color,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: const Color(0xFF71717A),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF18181B),
          ),
        ),
      ],
    );
  }

  Widget _buildFocusTag(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFFF1F5F9),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
        ),
      ),
      child: Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 12,
          color: const Color(0xFF475569),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildThisWeekSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'This Week',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF18181B),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildWeekStat(
                'Workouts',
                '4',
                '+1 from last week',
                const Color(0xFF16A34A),
                Icons.trending_up,
              ),
            ),
            const SizedBox(width: 24),
            Expanded(
              child: _buildWeekStat(
                'Streak',
                '12',
                'days active',
                const Color(0xFF3B82F6),
                Icons.local_fire_department,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWeekStat(String label, String value, String subtitle, Color color, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF71717A),
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              icon,
              size: 16,
              color: color,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF18181B),
          ),
        ),
        Text(
          subtitle,
          style: GoogleFonts.inter(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                Icons.fitness_center,
                'Workout',
                0,
                true,
              ),
              _buildNavItem(
                Icons.favorite_outline,
                'Health',
                1,
                false,
              ),
              _buildNavItem(
                Icons.calendar_today,
                'Calendar',
                2,
                false,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, int index, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 24,
            color: isSelected
                ? const Color(0xFF3B82F6)
                : const Color(0xFF71717A),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 12,
              color: isSelected
                  ? const Color(0xFF3B82F6)
                  : const Color(0xFF71717A),
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
