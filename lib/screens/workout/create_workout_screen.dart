import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/workout_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_overlay.dart';
import 'workout_detail_screen.dart';

class CreateWorkoutScreen extends StatefulWidget {
  const CreateWorkoutScreen({super.key});

  @override
  State<CreateWorkoutScreen> createState() => _CreateWorkoutScreenState();
}

class _CreateWorkoutScreenState extends State<CreateWorkoutScreen> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFFFFFF),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFF18181B),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Create Workout',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF18181B),
          ),
        ),
      ),
      body: Consumer<WorkoutProvider>(
        builder: (context, workoutProvider, child) {
          return LoadingOverlay(
            isLoading: workoutProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Text(
                    'New Workout',
                    style: GoogleFonts.inter(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF18181B),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Give your workout a name and description to get started.',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      color: const Color(0xFF71717A),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Form
                  FormBuilder(
                    key: _formKey,
                    child: Column(
                      children: [
                        // Workout Name
                        CustomTextField(
                          name: 'name',
                          label: 'Workout Name',
                          hint: 'e.g., Push Day, Morning Run, Full Body',
                          validators: [
                            FormBuilderValidators.required(),
                            FormBuilderValidators.minLength(2),
                            FormBuilderValidators.maxLength(50),
                          ],
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Notes/Description
                        CustomTextField(
                          name: 'notes',
                          label: 'Notes (Optional)',
                          hint: 'Add any notes about this workout...',
                          maxLines: 3,
                          validators: [
                            FormBuilderValidators.maxLength(500),
                          ],
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // Quick Templates Section
                        _buildQuickTemplatesSection(),
                        
                        const SizedBox(height: 32),
                        
                        // Create Button
                        CustomButton(
                          text: 'Create Workout',
                          onPressed: () => _handleCreateWorkout(workoutProvider),
                          isLoading: workoutProvider.isLoading,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Error Message
                        if (workoutProvider.errorMessage != null)
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFEF2F2),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: const Color(0xFFFECACA),
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.error_outline,
                                  color: Color(0xFFDC2626),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    workoutProvider.errorMessage!,
                                    style: GoogleFonts.inter(
                                      fontSize: 14,
                                      color: const Color(0xFFDC2626),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuickTemplatesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Templates',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF18181B),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Start with a pre-made template or create from scratch',
          style: GoogleFonts.inter(
            fontSize: 14,
            color: const Color(0xFF71717A),
          ),
        ),
        const SizedBox(height: 16),
        
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.3,
          children: [
            _buildTemplateCard(
              'Push Day',
              'Chest, Shoulders, Triceps',
              Icons.fitness_center,
              const Color(0xFF3B82F6),
              () => _useTemplate('Push Day'),
            ),
            _buildTemplateCard(
              'Pull Day',
              'Back, Biceps',
              Icons.sports_gymnastics,
              const Color(0xFF16A34A),
              () => _useTemplate('Pull Day'),
            ),
            _buildTemplateCard(
              'Leg Day',
              'Quads, Hamstrings, Glutes',
              Icons.directions_run,
              const Color(0xFFEF4444),
              () => _useTemplate('Leg Day'),
            ),
            _buildTemplateCard(
              'Full Body',
              'Complete workout',
              Icons.accessibility,
              const Color(0xFF8B5CF6),
              () => _useTemplate('Full Body'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTemplateCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE5E7EB)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                icon,
                size: 20,
                color: color,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              textAlign: TextAlign.center,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF18181B),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              textAlign: TextAlign.center,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: const Color(0xFF71717A),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _useTemplate(String templateName) {
    // Pre-fill the form with template name
    _formKey.currentState?.fields['name']?.didChange(templateName);
    
    // Add template-specific notes
    String notes = '';
    switch (templateName) {
      case 'Push Day':
        notes = 'Focus on chest, shoulders, and triceps exercises';
        break;
      case 'Pull Day':
        notes = 'Focus on back and biceps exercises';
        break;
      case 'Leg Day':
        notes = 'Focus on quadriceps, hamstrings, and glutes';
        break;
      case 'Full Body':
        notes = 'Complete workout targeting all major muscle groups';
        break;
    }
    
    _formKey.currentState?.fields['notes']?.didChange(notes);
  }

  Future<void> _handleCreateWorkout(WorkoutProvider workoutProvider) async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      
      workoutProvider.clearError();
      
      final workout = await workoutProvider.createWorkout(
        name: formData['name'],
        notes: formData['notes'],
      );
      
      if (workout != null && mounted) {
        // Navigate to the workout detail screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => WorkoutDetailScreen(workout: workout),
          ),
        );
      }
    }
  }
}
