import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/workout.dart';
import '../services/auth_service.dart';

class WorkoutService {
  static final SupabaseClient _client = SupabaseConfig.client;

  // Get all workouts for the current user
  static Future<List<Workout>> getUserWorkouts() async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from('workouts')
          .select('''
            *,
            workout_exercises (
              *,
              exercises (*)
            )
          ''')
          .eq('user_id', AuthService.currentUser!.id)
          .order('created_at', ascending: false);

      return response.map((json) => Workout.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Get a specific workout with exercises
  static Future<Workout?> getWorkout(String workoutId) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from('workouts')
          .select('''
            *,
            workout_exercises (
              *,
              exercises (*)
            )
          ''')
          .eq('id', workoutId)
          .eq('user_id', AuthService.currentUser!.id)
          .maybeSingle();

      if (response == null) return null;

      return Workout.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Create a new workout
  static Future<Workout> createWorkout({
    required String name,
    String? notes,
    String? aiDescription,
  }) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final workoutData = {
        'user_id': AuthService.currentUser!.id,
        'name': name,
        'notes': notes,
        'ai_description': aiDescription,
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _client
          .from('workouts')
          .insert(workoutData)
          .select()
          .single();

      return Workout.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Start a workout
  static Future<Workout> startWorkout(String workoutId) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from('workouts')
          .update({
            'start_time': DateTime.now().toIso8601String(),
            'is_active': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', workoutId)
          .eq('user_id', AuthService.currentUser!.id)
          .select()
          .single();

      return Workout.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Complete a workout
  static Future<Workout> completeWorkout(String workoutId, {
    int? duration,
    int? rating,
    String? notes,
  }) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from('workouts')
          .update({
            'end_time': DateTime.now().toIso8601String(),
            'is_completed': true,
            'duration': duration,
            'rating': rating,
            'notes': notes,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', workoutId)
          .eq('user_id', AuthService.currentUser!.id)
          .select()
          .single();

      return Workout.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Get all exercises
  static Future<List<Exercise>> getExercises({
    String? category,
    String? equipment,
    String? primaryMuscle,
  }) async {
    try {
      var query = _client.from('exercises').select();

      if (category != null) {
        query = query.eq('category', category);
      }
      if (equipment != null) {
        query = query.eq('equipment', equipment);
      }
      if (primaryMuscle != null) {
        query = query.eq('primary_muscle', primaryMuscle);
      }

      final response = await query.order('name');

      return response.map((json) => Exercise.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Add exercise to workout
  static Future<WorkoutExercise> addExerciseToWorkout({
    required String workoutId,
    required String exerciseId,
    required String exerciseName,
    int? sets,
    List<int>? reps,
    List<int>? weight,
    int? restInterval,
    int? orderIndex,
  }) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final exerciseData = {
        'workout_id': workoutId,
        'exercise_id': exerciseId,
        'name': exerciseName,
        'sets': sets,
        'reps': reps,
        'weight': weight,
        'rest_interval': restInterval,
        'order_index': orderIndex,
        'completed': false,
        'created_at': DateTime.now().toIso8601String(),
      };

      final response = await _client
          .from('workout_exercises')
          .insert(exerciseData)
          .select()
          .single();

      return WorkoutExercise.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Update workout exercise
  static Future<WorkoutExercise> updateWorkoutExercise({
    required String workoutExerciseId,
    int? sets,
    List<int>? reps,
    List<int>? weight,
    bool? completed,
    int? restInterval,
  }) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final updateData = <String, dynamic>{};
      if (sets != null) updateData['sets'] = sets;
      if (reps != null) updateData['reps'] = reps;
      if (weight != null) updateData['weight'] = weight;
      if (completed != null) updateData['completed'] = completed;
      if (restInterval != null) updateData['rest_interval'] = restInterval;

      final response = await _client
          .from('workout_exercises')
          .update(updateData)
          .eq('id', workoutExerciseId)
          .select()
          .single();

      return WorkoutExercise.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Get workout exercises for a specific workout
  static Future<List<WorkoutExercise>> getWorkoutExercises(String workoutId) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from('workout_exercises')
          .select('''
            *,
            exercises (*)
          ''')
          .eq('workout_id', workoutId)
          .order('order_index');

      return response.map((json) => WorkoutExercise.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Delete workout
  static Future<void> deleteWorkout(String workoutId) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      await _client
          .from('workouts')
          .delete()
          .eq('id', workoutId)
          .eq('user_id', AuthService.currentUser!.id);
    } catch (e) {
      rethrow;
    }
  }

  // Remove exercise from workout
  static Future<void> removeExerciseFromWorkout(String workoutExerciseId) async {
    try {
      if (!AuthService.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      await _client
          .from('workout_exercises')
          .delete()
          .eq('id', workoutExerciseId);
    } catch (e) {
      rethrow;
    }
  }
}
