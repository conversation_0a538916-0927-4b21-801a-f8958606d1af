import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/user_profile.dart';

class AuthService {
  static final SupabaseClient _client = SupabaseConfig.client;
  static final GoTrueClient _auth = SupabaseConfig.auth;

  // Get current user
  static User? get currentUser => _auth.currentUser;
  
  // Get current session
  static Session? get currentSession => _auth.currentSession;
  
  // Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  // Auth state stream
  static Stream<AuthState> get authStateChanges => _auth.onAuthStateChange;

  // Sign up with email and password
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final response = await _auth.signUp(
        email: email,
        password: password,
        data: displayName != null ? {'display_name': displayName} : null,
      );
      
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Sign in with email and password
  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // Reset password
  static Future<void> resetPassword({required String email}) async {
    try {
      await _auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // Update password
  static Future<UserResponse> updatePassword({
    required String newPassword,
  }) async {
    try {
      final response = await _auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Resend email confirmation
  static Future<void> resendEmailConfirmation({required String email}) async {
    try {
      await _auth.resend(
        type: OtpType.signup,
        email: email,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get user profile from profiles table
  static Future<UserProfile?> getUserProfile() async {
    try {
      if (!isAuthenticated) return null;
      
      final response = await _client
          .from('profiles')
          .select()
          .eq('id', currentUser!.id)
          .maybeSingle();
      
      if (response == null) return null;
      
      return UserProfile.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Create or update user profile
  static Future<UserProfile> upsertUserProfile(UserProfile profile) async {
    try {
      final response = await _client
          .from('profiles')
          .upsert(profile.toJson())
          .select()
          .single();
      
      return UserProfile.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Check if user has completed onboarding
  static Future<bool> hasCompletedOnboarding() async {
    try {
      final profile = await getUserProfile();
      return profile?.onboardingCompleted ?? false;
    } catch (e) {
      return false;
    }
  }

  // Mark onboarding as completed
  static Future<void> completeOnboarding() async {
    try {
      if (!isAuthenticated) throw Exception('User not authenticated');
      
      await _client
          .from('profiles')
          .update({'onboarding_completed': true})
          .eq('id', currentUser!.id);
    } catch (e) {
      rethrow;
    }
  }
}
