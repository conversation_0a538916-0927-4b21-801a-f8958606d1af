class UserProfile {
  final String id;
  final String? email;
  final String? displayName;
  final String? gender;
  final int? age;
  final double? height;
  final String? heightUnit;
  final double? weight;
  final String? weightUnit;
  final String? fitnessGoalPrimary;
  final List<String>? fitnessGoalsArray;
  final List<String>? equipment;
  final List<String>? workoutDays;
  final String? workoutDurationPreference;
  final int? preferredWorkoutDaysCount;
  final String? trainingExperienceLevel;
  final int? cardioFitnessLevel;
  final int? weightliftingFitnessLevel;
  final List<String>? healthConditions;
  final List<String>? dietaryRestrictions;
  final List<String>? physicalLimitations;
  final String? sportOfChoice;
  final String? specificSportActivity;
  final List<String>? exercisesToAvoid;
  final List<String>? workoutEnvironment;
  final List<String>? exercisePreferences;
  final bool? takingSupplements;
  final List<String>? supplements;
  final int? mealsPerDay;
  final int? calorieGoal;
  final String? sleepQuality;
  final String? additionalHealthInfo;
  final bool onboardingCompleted;
  final bool fitnessAssessmentCompleted;
  final bool hasCompletedPreferences;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.id,
    this.email,
    this.displayName,
    this.gender,
    this.age,
    this.height,
    this.heightUnit = 'cm',
    this.weight,
    this.weightUnit = 'kg',
    this.fitnessGoalPrimary,
    this.fitnessGoalsArray,
    this.equipment,
    this.workoutDays,
    this.workoutDurationPreference,
    this.preferredWorkoutDaysCount,
    this.trainingExperienceLevel,
    this.cardioFitnessLevel,
    this.weightliftingFitnessLevel,
    this.healthConditions,
    this.dietaryRestrictions,
    this.physicalLimitations,
    this.sportOfChoice,
    this.specificSportActivity,
    this.exercisesToAvoid,
    this.workoutEnvironment,
    this.exercisePreferences,
    this.takingSupplements,
    this.supplements,
    this.mealsPerDay,
    this.calorieGoal,
    this.sleepQuality,
    this.additionalHealthInfo,
    this.onboardingCompleted = false,
    this.fitnessAssessmentCompleted = false,
    this.hasCompletedPreferences = false,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as String,
      email: json['email'] as String?,
      displayName: json['display_name'] as String?,
      gender: json['gender'] as String?,
      age: json['age'] as int?,
      height: json['height']?.toDouble(),
      heightUnit: json['height_unit'] as String? ?? 'cm',
      weight: json['weight']?.toDouble(),
      weightUnit: json['weight_unit'] as String? ?? 'kg',
      fitnessGoalPrimary: json['fitness_goal_primary'] as String?,
      fitnessGoalsArray: (json['fitness_goals_array'] as List<dynamic>?)?.cast<String>(),
      equipment: (json['equipment'] as List<dynamic>?)?.cast<String>(),
      workoutDays: (json['workout_days'] as List<dynamic>?)?.cast<String>(),
      workoutDurationPreference: json['workout_duration_preference'] as String?,
      preferredWorkoutDaysCount: json['preferred_workout_days_count'] as int?,
      trainingExperienceLevel: json['training_experience_level'] as String?,
      cardioFitnessLevel: json['cardio_fitness_level'] as int?,
      weightliftingFitnessLevel: json['weightlifting_fitness_level'] as int?,
      healthConditions: (json['health_conditions'] as List<dynamic>?)?.cast<String>(),
      dietaryRestrictions: (json['dietary_restrictions'] as List<dynamic>?)?.cast<String>(),
      physicalLimitations: (json['physical_limitations'] as List<dynamic>?)?.cast<String>(),
      sportOfChoice: json['sport_of_choice'] as String?,
      specificSportActivity: json['specific_sport_activity'] as String?,
      exercisesToAvoid: (json['exercises_to_avoid'] as List<dynamic>?)?.cast<String>(),
      workoutEnvironment: (json['workout_environment'] as List<dynamic>?)?.cast<String>(),
      exercisePreferences: (json['exercise_preferences'] as List<dynamic>?)?.cast<String>(),
      takingSupplements: json['taking_supplements'] as bool?,
      supplements: (json['supplements'] as List<dynamic>?)?.cast<String>(),
      mealsPerDay: json['meals_per_day'] as int?,
      calorieGoal: json['caloric_goal'] as int?,
      sleepQuality: json['sleep_quality'] as String?,
      additionalHealthInfo: json['additional_health_info'] as String?,
      onboardingCompleted: json['onboarding_completed'] as bool? ?? false,
      fitnessAssessmentCompleted: json['fitness_assessment_completed'] as bool? ?? false,
      hasCompletedPreferences: json['has_completed_preferences'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'display_name': displayName,
      'gender': gender,
      'age': age,
      'height': height,
      'height_unit': heightUnit,
      'weight': weight,
      'weight_unit': weightUnit,
      'fitness_goal_primary': fitnessGoalPrimary,
      'fitness_goals_array': fitnessGoalsArray,
      'equipment': equipment,
      'workout_days': workoutDays,
      'workout_duration_preference': workoutDurationPreference,
      'preferred_workout_days_count': preferredWorkoutDaysCount,
      'training_experience_level': trainingExperienceLevel,
      'cardio_fitness_level': cardioFitnessLevel,
      'weightlifting_fitness_level': weightliftingFitnessLevel,
      'health_conditions': healthConditions,
      'dietary_restrictions': dietaryRestrictions,
      'physical_limitations': physicalLimitations,
      'sport_of_choice': sportOfChoice,
      'specific_sport_activity': specificSportActivity,
      'exercises_to_avoid': exercisesToAvoid,
      'workout_environment': workoutEnvironment,
      'exercise_preferences': exercisePreferences,
      'taking_supplements': takingSupplements,
      'supplements': supplements,
      'meals_per_day': mealsPerDay,
      'caloric_goal': calorieGoal,
      'sleep_quality': sleepQuality,
      'additional_health_info': additionalHealthInfo,
      'onboarding_completed': onboardingCompleted,
      'fitness_assessment_completed': fitnessAssessmentCompleted,
      'has_completed_preferences': hasCompletedPreferences,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserProfile copyWith({
    String? id,
    String? email,
    String? displayName,
    String? gender,
    int? age,
    double? height,
    String? heightUnit,
    double? weight,
    String? weightUnit,
    String? fitnessGoalPrimary,
    List<String>? fitnessGoalsArray,
    List<String>? equipment,
    List<String>? workoutDays,
    String? workoutDurationPreference,
    int? preferredWorkoutDaysCount,
    String? trainingExperienceLevel,
    int? cardioFitnessLevel,
    int? weightliftingFitnessLevel,
    List<String>? healthConditions,
    List<String>? dietaryRestrictions,
    List<String>? physicalLimitations,
    String? sportOfChoice,
    String? specificSportActivity,
    List<String>? exercisesToAvoid,
    List<String>? workoutEnvironment,
    List<String>? exercisePreferences,
    bool? takingSupplements,
    List<String>? supplements,
    int? mealsPerDay,
    int? calorieGoal,
    String? sleepQuality,
    String? additionalHealthInfo,
    bool? onboardingCompleted,
    bool? fitnessAssessmentCompleted,
    bool? hasCompletedPreferences,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      height: height ?? this.height,
      heightUnit: heightUnit ?? this.heightUnit,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
      fitnessGoalPrimary: fitnessGoalPrimary ?? this.fitnessGoalPrimary,
      fitnessGoalsArray: fitnessGoalsArray ?? this.fitnessGoalsArray,
      equipment: equipment ?? this.equipment,
      workoutDays: workoutDays ?? this.workoutDays,
      workoutDurationPreference: workoutDurationPreference ?? this.workoutDurationPreference,
      preferredWorkoutDaysCount: preferredWorkoutDaysCount ?? this.preferredWorkoutDaysCount,
      trainingExperienceLevel: trainingExperienceLevel ?? this.trainingExperienceLevel,
      cardioFitnessLevel: cardioFitnessLevel ?? this.cardioFitnessLevel,
      weightliftingFitnessLevel: weightliftingFitnessLevel ?? this.weightliftingFitnessLevel,
      healthConditions: healthConditions ?? this.healthConditions,
      dietaryRestrictions: dietaryRestrictions ?? this.dietaryRestrictions,
      physicalLimitations: physicalLimitations ?? this.physicalLimitations,
      sportOfChoice: sportOfChoice ?? this.sportOfChoice,
      specificSportActivity: specificSportActivity ?? this.specificSportActivity,
      exercisesToAvoid: exercisesToAvoid ?? this.exercisesToAvoid,
      workoutEnvironment: workoutEnvironment ?? this.workoutEnvironment,
      exercisePreferences: exercisePreferences ?? this.exercisePreferences,
      takingSupplements: takingSupplements ?? this.takingSupplements,
      supplements: supplements ?? this.supplements,
      mealsPerDay: mealsPerDay ?? this.mealsPerDay,
      calorieGoal: calorieGoal ?? this.calorieGoal,
      sleepQuality: sleepQuality ?? this.sleepQuality,
      additionalHealthInfo: additionalHealthInfo ?? this.additionalHealthInfo,
      onboardingCompleted: onboardingCompleted ?? this.onboardingCompleted,
      fitnessAssessmentCompleted: fitnessAssessmentCompleted ?? this.fitnessAssessmentCompleted,
      hasCompletedPreferences: hasCompletedPreferences ?? this.hasCompletedPreferences,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
