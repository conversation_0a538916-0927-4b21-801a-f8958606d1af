class Exercise {
  final String id;
  final String name;
  final String? description;
  final String? videoUrl;
  final String? primaryMuscle;
  final String? secondaryMuscle;
  final String? equipment;
  final String? instructions;
  final String? category;
  final String? verticalVideo;
  final DateTime createdAt;

  Exercise({
    required this.id,
    required this.name,
    this.description,
    this.videoUrl,
    this.primaryMuscle,
    this.secondaryMuscle,
    this.equipment,
    this.instructions,
    this.category,
    this.verticalVideo,
    required this.createdAt,
  });

  factory Exercise.fromJson(Map<String, dynamic> json) {
    return Exercise(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      videoUrl: json['video_url'] as String?,
      primaryMuscle: json['primary_muscle'] as String?,
      secondaryMuscle: json['secondary_muscle'] as String?,
      equipment: json['equipment'] as String?,
      instructions: json['instructions'] as String?,
      category: json['category'] as String?,
      verticalVideo: json['vertical_video'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'video_url': videoUrl,
      'primary_muscle': primaryMuscle,
      'secondary_muscle': secondaryMuscle,
      'equipment': equipment,
      'instructions': instructions,
      'category': category,
      'vertical_video': verticalVideo,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class WorkoutExercise {
  final String id;
  final String workoutId;
  final String exerciseId;
  final String name;
  final int? sets;
  final int? orderIndex;
  final int? order;
  final bool completed;
  final int? restInterval;
  final List<int>? weight;
  final List<int>? reps;
  final DateTime? createdAt;
  final Exercise? exercise; // Populated when joined

  WorkoutExercise({
    required this.id,
    required this.workoutId,
    required this.exerciseId,
    required this.name,
    this.sets,
    this.orderIndex,
    this.order,
    this.completed = false,
    this.restInterval,
    this.weight,
    this.reps,
    this.createdAt,
    this.exercise,
  });

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) {
    return WorkoutExercise(
      id: json['id'] as String,
      workoutId: json['workout_id'] as String,
      exerciseId: json['exercise_id'] as String,
      name: json['name'] as String,
      sets: json['sets'] as int?,
      orderIndex: json['order_index'] as int?,
      order: json['order'] as int?,
      completed: json['completed'] as bool? ?? false,
      restInterval: json['rest_interval'] as int?,
      weight: (json['weight'] as List<dynamic>?)?.cast<int>(),
      reps: (json['reps'] as List<dynamic>?)?.cast<int>(),
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      exercise: json['exercises'] != null 
          ? Exercise.fromJson(json['exercises'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'workout_id': workoutId,
      'exercise_id': exerciseId,
      'name': name,
      'sets': sets,
      'order_index': orderIndex,
      'order': order,
      'completed': completed,
      'rest_interval': restInterval,
      'weight': weight,
      'reps': reps,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  WorkoutExercise copyWith({
    String? id,
    String? workoutId,
    String? exerciseId,
    String? name,
    int? sets,
    int? orderIndex,
    int? order,
    bool? completed,
    int? restInterval,
    List<int>? weight,
    List<int>? reps,
    DateTime? createdAt,
    Exercise? exercise,
  }) {
    return WorkoutExercise(
      id: id ?? this.id,
      workoutId: workoutId ?? this.workoutId,
      exerciseId: exerciseId ?? this.exerciseId,
      name: name ?? this.name,
      sets: sets ?? this.sets,
      orderIndex: orderIndex ?? this.orderIndex,
      order: order ?? this.order,
      completed: completed ?? this.completed,
      restInterval: restInterval ?? this.restInterval,
      weight: weight ?? this.weight,
      reps: reps ?? this.reps,
      createdAt: createdAt ?? this.createdAt,
      exercise: exercise ?? this.exercise,
    );
  }
}

class Workout {
  final String id;
  final String userId;
  final String name;
  final DateTime? startTime;
  final DateTime? endTime;
  final bool isActive;
  final int? duration;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? rating;
  final bool isMinimized;
  final Map<String, dynamic>? lastState;
  final bool? isCompleted;
  final int? sessionOrder;
  final String? aiDescription;
  final List<WorkoutExercise>? exercises;

  Workout({
    required this.id,
    required this.userId,
    required this.name,
    this.startTime,
    this.endTime,
    this.isActive = true,
    this.duration,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.rating,
    this.isMinimized = false,
    this.lastState,
    this.isCompleted,
    this.sessionOrder,
    this.aiDescription,
    this.exercises,
  });

  factory Workout.fromJson(Map<String, dynamic> json) {
    return Workout(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      name: json['name'] as String,
      startTime: json['start_time'] != null 
          ? DateTime.parse(json['start_time'] as String) 
          : null,
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time'] as String) 
          : null,
      isActive: json['is_active'] as bool? ?? true,
      duration: json['duration'] as int?,
      notes: json['notes'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      rating: json['rating'] as int?,
      isMinimized: json['is_minimized'] as bool? ?? false,
      lastState: json['last_state'] as Map<String, dynamic>?,
      isCompleted: json['is_completed'] as bool?,
      sessionOrder: json['session_order'] as int?,
      aiDescription: json['ai_description'] as String?,
      exercises: json['workout_exercises'] != null
          ? (json['workout_exercises'] as List<dynamic>)
              .map((e) => WorkoutExercise.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'is_active': isActive,
      'duration': duration,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'rating': rating,
      'is_minimized': isMinimized,
      'last_state': lastState,
      'is_completed': isCompleted,
      'session_order': sessionOrder,
      'ai_description': aiDescription,
    };
  }

  Workout copyWith({
    String? id,
    String? userId,
    String? name,
    DateTime? startTime,
    DateTime? endTime,
    bool? isActive,
    int? duration,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? rating,
    bool? isMinimized,
    Map<String, dynamic>? lastState,
    bool? isCompleted,
    int? sessionOrder,
    String? aiDescription,
    List<WorkoutExercise>? exercises,
  }) {
    return Workout(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isActive: isActive ?? this.isActive,
      duration: duration ?? this.duration,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rating: rating ?? this.rating,
      isMinimized: isMinimized ?? this.isMinimized,
      lastState: lastState ?? this.lastState,
      isCompleted: isCompleted ?? this.isCompleted,
      sessionOrder: sessionOrder ?? this.sessionOrder,
      aiDescription: aiDescription ?? this.aiDescription,
      exercises: exercises ?? this.exercises,
    );
  }
}
