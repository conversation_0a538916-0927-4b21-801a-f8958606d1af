import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_service.dart';
import '../models/user_profile.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  loading,
}

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  User? _user;
  UserProfile? _userProfile;
  String? _errorMessage;
  StreamSubscription<AuthState>? _authSubscription;

  AuthStatus get status => _status;
  User? get user => _user;
  UserProfile? get userProfile => _userProfile;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isLoading => _status == AuthStatus.loading;

  AuthProvider() {
    _initialize();
  }

  void _initialize() {
    // Set initial state based on current session
    final session = AuthService.currentSession;
    if (session != null) {
      _user = session.user;
      _status = AuthStatus.authenticated;
      _loadUserProfile();
    } else {
      _status = AuthStatus.unauthenticated;
    }

    // Listen to auth state changes
    _authSubscription = AuthService.authStateChanges.listen((authState) {
      _handleAuthStateChange(authState);
    });
  }

  void _handleAuthStateChange(AuthState authState) {
    switch (authState.event) {
      case AuthChangeEvent.signedIn:
        _user = authState.session?.user;
        _status = AuthStatus.authenticated;
        _loadUserProfile();
        break;
      case AuthChangeEvent.signedOut:
        _user = null;
        _userProfile = null;
        _status = AuthStatus.unauthenticated;
        break;
      case AuthChangeEvent.userUpdated:
        _user = authState.session?.user;
        _loadUserProfile();
        break;
      default:
        break;
    }
    notifyListeners();
  }

  Future<void> _loadUserProfile() async {
    try {
      _userProfile = await AuthService.getUserProfile();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      _setLoading();
      _clearError();

      final response = await AuthService.signUp(
        email: email,
        password: password,
        displayName: displayName,
      );

      if (response.user != null) {
        // Create initial profile
        final profile = UserProfile(
          id: response.user!.id,
          email: email,
          displayName: displayName,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await AuthService.upsertUserProfile(profile);
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setNotLoading();
    }
  }

  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading();
      _clearError();

      final response = await AuthService.signIn(
        email: email,
        password: password,
      );

      return response.user != null;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setNotLoading();
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading();
      await AuthService.signOut();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setNotLoading();
    }
  }

  Future<bool> resetPassword({required String email}) async {
    try {
      _setLoading();
      _clearError();

      await AuthService.resetPassword(email: email);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setNotLoading();
    }
  }

  Future<bool> resendEmailConfirmation({required String email}) async {
    try {
      _setLoading();
      _clearError();

      await AuthService.resendEmailConfirmation(email: email);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setNotLoading();
    }
  }

  Future<bool> updateUserProfile(UserProfile profile) async {
    try {
      _setLoading();
      _clearError();

      _userProfile = await AuthService.upsertUserProfile(profile);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setNotLoading();
    }
  }

  Future<bool> completeOnboarding() async {
    try {
      await AuthService.completeOnboarding();
      await _loadUserProfile(); // Reload to get updated onboarding status
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  void _setLoading() {
    _status = AuthStatus.loading;
    notifyListeners();
  }

  void _setNotLoading() {
    _status = _user != null ? AuthStatus.authenticated : AuthStatus.unauthenticated;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
