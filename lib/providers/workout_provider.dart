import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/workout.dart';
import '../services/workout_service.dart';

enum WorkoutStatus {
  idle,
  loading,
  error,
}

class WorkoutProvider extends ChangeNotifier {
  WorkoutStatus _status = WorkoutStatus.idle;
  List<Workout> _workouts = [];
  Workout? _currentWorkout;
  List<Exercise> _exercises = [];
  String? _errorMessage;
  Timer? _workoutTimer;
  int _elapsedSeconds = 0;

  // Getters
  WorkoutStatus get status => _status;
  List<Workout> get workouts => _workouts;
  Workout? get currentWorkout => _currentWorkout;
  List<Exercise> get exercises => _exercises;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _status == WorkoutStatus.loading;
  int get elapsedSeconds => _elapsedSeconds;
  String get formattedElapsedTime => _formatDuration(_elapsedSeconds);

  // Load user workouts
  Future<void> loadWorkouts() async {
    try {
      _setLoading();
      _clearError();

      _workouts = await WorkoutService.getUserWorkouts();
      _setIdle();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Load exercises
  Future<void> loadExercises({
    String? category,
    String? equipment,
    String? primaryMuscle,
  }) async {
    try {
      _setLoading();
      _clearError();

      _exercises = await WorkoutService.getExercises(
        category: category,
        equipment: equipment,
        primaryMuscle: primaryMuscle,
      );
      _setIdle();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Create new workout
  Future<Workout?> createWorkout({
    required String name,
    String? notes,
    String? aiDescription,
  }) async {
    try {
      _setLoading();
      _clearError();

      final workout = await WorkoutService.createWorkout(
        name: name,
        notes: notes,
        aiDescription: aiDescription,
      );

      _workouts.insert(0, workout);
      _setIdle();
      return workout;
    } catch (e) {
      _setError(e.toString());
      return null;
    }
  }

  // Start workout
  Future<bool> startWorkout(String workoutId) async {
    try {
      _setLoading();
      _clearError();

      _currentWorkout = await WorkoutService.startWorkout(workoutId);
      _startTimer();
      _setIdle();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Complete workout
  Future<bool> completeWorkout({
    int? rating,
    String? notes,
  }) async {
    try {
      if (_currentWorkout == null) return false;

      _setLoading();
      _clearError();

      _stopTimer();
      
      final completedWorkout = await WorkoutService.completeWorkout(
        _currentWorkout!.id,
        duration: _elapsedSeconds,
        rating: rating,
        notes: notes,
      );

      // Update the workout in the list
      final index = _workouts.indexWhere((w) => w.id == completedWorkout.id);
      if (index != -1) {
        _workouts[index] = completedWorkout;
      }

      _currentWorkout = null;
      _elapsedSeconds = 0;
      _setIdle();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Load specific workout
  Future<void> loadWorkout(String workoutId) async {
    try {
      _setLoading();
      _clearError();

      _currentWorkout = await WorkoutService.getWorkout(workoutId);
      _setIdle();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Add exercise to current workout
  Future<bool> addExerciseToWorkout({
    required String exerciseId,
    required String exerciseName,
    int? sets,
    List<int>? reps,
    List<int>? weight,
    int? restInterval,
  }) async {
    try {
      if (_currentWorkout == null) return false;

      _clearError();

      final workoutExercise = await WorkoutService.addExerciseToWorkout(
        workoutId: _currentWorkout!.id,
        exerciseId: exerciseId,
        exerciseName: exerciseName,
        sets: sets,
        reps: reps,
        weight: weight,
        restInterval: restInterval,
        orderIndex: (_currentWorkout!.exercises?.length ?? 0) + 1,
      );

      // Update current workout with new exercise
      final updatedExercises = List<WorkoutExercise>.from(_currentWorkout!.exercises ?? []);
      updatedExercises.add(workoutExercise);
      
      _currentWorkout = _currentWorkout!.copyWith(exercises: updatedExercises);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Update workout exercise
  Future<bool> updateWorkoutExercise({
    required String workoutExerciseId,
    int? sets,
    List<int>? reps,
    List<int>? weight,
    bool? completed,
    int? restInterval,
  }) async {
    try {
      _clearError();

      final updatedExercise = await WorkoutService.updateWorkoutExercise(
        workoutExerciseId: workoutExerciseId,
        sets: sets,
        reps: reps,
        weight: weight,
        completed: completed,
        restInterval: restInterval,
      );

      // Update the exercise in current workout
      if (_currentWorkout?.exercises != null) {
        final exercises = _currentWorkout!.exercises!;
        final index = exercises.indexWhere((e) => e.id == workoutExerciseId);
        if (index != -1) {
          exercises[index] = updatedExercise;
          _currentWorkout = _currentWorkout!.copyWith(exercises: exercises);
          notifyListeners();
        }
      }

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Delete workout
  Future<bool> deleteWorkout(String workoutId) async {
    try {
      _clearError();

      await WorkoutService.deleteWorkout(workoutId);
      _workouts.removeWhere((w) => w.id == workoutId);
      
      if (_currentWorkout?.id == workoutId) {
        _currentWorkout = null;
        _stopTimer();
        _elapsedSeconds = 0;
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Timer methods
  void _startTimer() {
    _stopTimer(); // Stop any existing timer
    _elapsedSeconds = 0;
    _workoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _elapsedSeconds++;
      notifyListeners();
    });
  }

  void _stopTimer() {
    _workoutTimer?.cancel();
    _workoutTimer = null;
  }

  void pauseTimer() {
    _workoutTimer?.cancel();
    _workoutTimer = null;
    notifyListeners();
  }

  void resumeTimer() {
    if (_workoutTimer == null && _currentWorkout != null) {
      _workoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _elapsedSeconds++;
        notifyListeners();
      });
    }
  }

  // Helper methods
  void _setLoading() {
    _status = WorkoutStatus.loading;
    notifyListeners();
  }

  void _setIdle() {
    _status = WorkoutStatus.idle;
    notifyListeners();
  }

  void _setError(String error) {
    _status = WorkoutStatus.error;
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    _stopTimer();
    super.dispose();
  }
}
